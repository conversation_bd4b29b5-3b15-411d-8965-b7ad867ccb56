<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="10" failures="10" errors="0" time="1.243">
  <testsuite name="AuthController" errors="0" failures="10" skipped="0" timestamp="2025-08-12T05:38:19" time="1.094" tests="10">
    <testcase classname="AuthController › POST /api/v1/auth/login › successful login" name="should return 200 and auth token for valid credentials" time="0.005">
      <failure>TypeError: AuthService.mockImplementation is not a function
    at Object.&lt;anonymous&gt; (/home/<USER>/paragon-api/tests/controllers/AuthController.test.js:37:19)
    at Promise.finally.completed (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)
    at _callCircusHook (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:978:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:943:5)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:839:13)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:829:11)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:829:11)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:829:11)
    at run (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:757:3)
    at runAndTransformResultsToJestFormat (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)
    at jestAdapter (/home/<USER>/paragon-api/node_modules/jest-circus/build/runner.js:101:19)
    at runTestInternal (/home/<USER>/paragon-api/node_modules/jest-runner/build/index.js:275:16)
    at runTest (/home/<USER>/paragon-api/node_modules/jest-runner/build/index.js:343:7)</failure>
    </testcase>
    <testcase classname="AuthController › POST /api/v1/auth/login › successful login" name="should handle email normalization" time="0.003">
      <failure>TypeError: AuthService.mockImplementation is not a function
    at Object.&lt;anonymous&gt; (/home/<USER>/paragon-api/tests/controllers/AuthController.test.js:37:19)
    at Promise.finally.completed (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)
    at _callCircusHook (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:978:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:943:5)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:839:13)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:829:11)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:829:11)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:829:11)
    at run (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:757:3)
    at runAndTransformResultsToJestFormat (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)
    at jestAdapter (/home/<USER>/paragon-api/node_modules/jest-circus/build/runner.js:101:19)
    at runTestInternal (/home/<USER>/paragon-api/node_modules/jest-runner/build/index.js:275:16)
    at runTest (/home/<USER>/paragon-api/node_modules/jest-runner/build/index.js:343:7)</failure>
    </testcase>
    <testcase classname="AuthController › POST /api/v1/auth/login › validation errors" name="should return 400 for missing email" time="0.002">
      <failure>TypeError: AuthService.mockImplementation is not a function
    at Object.&lt;anonymous&gt; (/home/<USER>/paragon-api/tests/controllers/AuthController.test.js:37:19)
    at Promise.finally.completed (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)
    at _callCircusHook (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:978:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:943:5)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:839:13)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:829:11)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:829:11)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:829:11)
    at run (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:757:3)
    at runAndTransformResultsToJestFormat (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)
    at jestAdapter (/home/<USER>/paragon-api/node_modules/jest-circus/build/runner.js:101:19)
    at runTestInternal (/home/<USER>/paragon-api/node_modules/jest-runner/build/index.js:275:16)
    at runTest (/home/<USER>/paragon-api/node_modules/jest-runner/build/index.js:343:7)</failure>
    </testcase>
    <testcase classname="AuthController › POST /api/v1/auth/login › validation errors" name="should return 400 for missing password" time="0.001">
      <failure>TypeError: AuthService.mockImplementation is not a function
    at Object.&lt;anonymous&gt; (/home/<USER>/paragon-api/tests/controllers/AuthController.test.js:37:19)
    at Promise.finally.completed (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)
    at _callCircusHook (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:978:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:943:5)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:839:13)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:829:11)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:829:11)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:829:11)
    at run (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:757:3)
    at runAndTransformResultsToJestFormat (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)
    at jestAdapter (/home/<USER>/paragon-api/node_modules/jest-circus/build/runner.js:101:19)
    at runTestInternal (/home/<USER>/paragon-api/node_modules/jest-runner/build/index.js:275:16)
    at runTest (/home/<USER>/paragon-api/node_modules/jest-runner/build/index.js:343:7)</failure>
    </testcase>
    <testcase classname="AuthController › POST /api/v1/auth/login › validation errors" name="should return 400 for invalid email format" time="0.012">
      <failure>TypeError: AuthService.mockImplementation is not a function
    at Object.&lt;anonymous&gt; (/home/<USER>/paragon-api/tests/controllers/AuthController.test.js:37:19)
    at Promise.finally.completed (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)
    at _callCircusHook (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:978:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:943:5)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:839:13)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:829:11)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:829:11)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:829:11)
    at run (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:757:3)
    at runAndTransformResultsToJestFormat (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)
    at jestAdapter (/home/<USER>/paragon-api/node_modules/jest-circus/build/runner.js:101:19)
    at runTestInternal (/home/<USER>/paragon-api/node_modules/jest-runner/build/index.js:275:16)
    at runTest (/home/<USER>/paragon-api/node_modules/jest-runner/build/index.js:343:7)</failure>
    </testcase>
    <testcase classname="AuthController › POST /api/v1/auth/login › authentication errors" name="should return 404 for non-existent user" time="0.001">
      <failure>TypeError: AuthService.mockImplementation is not a function
    at Object.&lt;anonymous&gt; (/home/<USER>/paragon-api/tests/controllers/AuthController.test.js:37:19)
    at Promise.finally.completed (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)
    at _callCircusHook (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:978:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:943:5)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:839:13)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:829:11)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:829:11)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:829:11)
    at run (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:757:3)
    at runAndTransformResultsToJestFormat (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)
    at jestAdapter (/home/<USER>/paragon-api/node_modules/jest-circus/build/runner.js:101:19)
    at runTestInternal (/home/<USER>/paragon-api/node_modules/jest-runner/build/index.js:275:16)
    at runTest (/home/<USER>/paragon-api/node_modules/jest-runner/build/index.js:343:7)</failure>
    </testcase>
    <testcase classname="AuthController › POST /api/v1/auth/login › authentication errors" name="should return 400 for invalid password" time="0.001">
      <failure>TypeError: AuthService.mockImplementation is not a function
    at Object.&lt;anonymous&gt; (/home/<USER>/paragon-api/tests/controllers/AuthController.test.js:37:19)
    at Promise.finally.completed (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)
    at _callCircusHook (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:978:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:943:5)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:839:13)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:829:11)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:829:11)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:829:11)
    at run (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:757:3)
    at runAndTransformResultsToJestFormat (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)
    at jestAdapter (/home/<USER>/paragon-api/node_modules/jest-circus/build/runner.js:101:19)
    at runTestInternal (/home/<USER>/paragon-api/node_modules/jest-runner/build/index.js:275:16)
    at runTest (/home/<USER>/paragon-api/node_modules/jest-runner/build/index.js:343:7)</failure>
    </testcase>
    <testcase classname="AuthController › POST /api/v1/auth/login › server errors" name="should return 500 for unexpected errors" time="0.001">
      <failure>TypeError: AuthService.mockImplementation is not a function
    at Object.&lt;anonymous&gt; (/home/<USER>/paragon-api/tests/controllers/AuthController.test.js:37:19)
    at Promise.finally.completed (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)
    at _callCircusHook (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:978:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:943:5)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:839:13)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:829:11)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:829:11)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:829:11)
    at run (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:757:3)
    at runAndTransformResultsToJestFormat (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)
    at jestAdapter (/home/<USER>/paragon-api/node_modules/jest-circus/build/runner.js:101:19)
    at runTestInternal (/home/<USER>/paragon-api/node_modules/jest-runner/build/index.js:275:16)
    at runTest (/home/<USER>/paragon-api/node_modules/jest-runner/build/index.js:343:7)</failure>
    </testcase>
    <testcase classname="AuthController › POST /api/v1/auth/login › edge cases" name="should handle empty request body" time="0.002">
      <failure>TypeError: AuthService.mockImplementation is not a function
    at Object.&lt;anonymous&gt; (/home/<USER>/paragon-api/tests/controllers/AuthController.test.js:37:19)
    at Promise.finally.completed (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)
    at _callCircusHook (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:978:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:943:5)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:839:13)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:829:11)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:829:11)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:829:11)
    at run (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:757:3)
    at runAndTransformResultsToJestFormat (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)
    at jestAdapter (/home/<USER>/paragon-api/node_modules/jest-circus/build/runner.js:101:19)
    at runTestInternal (/home/<USER>/paragon-api/node_modules/jest-runner/build/index.js:275:16)
    at runTest (/home/<USER>/paragon-api/node_modules/jest-runner/build/index.js:343:7)</failure>
    </testcase>
    <testcase classname="AuthController › POST /api/v1/auth/login › edge cases" name="should handle malformed JSON" time="0.001">
      <failure>TypeError: AuthService.mockImplementation is not a function
    at Object.&lt;anonymous&gt; (/home/<USER>/paragon-api/tests/controllers/AuthController.test.js:37:19)
    at Promise.finally.completed (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)
    at _callCircusHook (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:978:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:943:5)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:839:13)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:829:11)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:829:11)
    at _runTestsForDescribeBlock (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:829:11)
    at run (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:757:3)
    at runAndTransformResultsToJestFormat (/home/<USER>/paragon-api/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)
    at jestAdapter (/home/<USER>/paragon-api/node_modules/jest-circus/build/runner.js:101:19)
    at runTestInternal (/home/<USER>/paragon-api/node_modules/jest-runner/build/index.js:275:16)
    at runTest (/home/<USER>/paragon-api/node_modules/jest-runner/build/index.js:343:7)</failure>
    </testcase>
  </testsuite>
</testsuites>