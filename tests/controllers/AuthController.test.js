const request = require('supertest');
const app = require('../../src/app');
const AuthService = require('../../src/services/AuthService');
const LoginInput = require('../../src/inputs/LoginInput');
const AuthOutput = require('../../src/outputs/AuthOutput');
const InvalidError = require('../../src/errors/InvalidError');
const NotFoundError = require('../../src/errors/NotFoundError');

// Mock dependencies
jest.mock('../../src/services/AuthService');
jest.mock('../../src/inputs/LoginInput');
jest.mock('../../src/outputs/AuthOutput');

describe('AuthController', () => {
  describe('POST /api/v1/auth/login', () => {
    let mockAuthService;
    let mockLoginInput;
    let mockAuthOutput;

    beforeEach(() => {
      // Reset all mocks
      jest.clearAllMocks();

      // Create mock instances
      mockAuthService = {
        login: jest.fn(),
      };
      mockLoginInput = {
        validate: jest.fn(),
        output: jest.fn(),
      };
      mockAuthOutput = {
        renderJson: jest.fn(),
      };

      // Mock constructors
      AuthService.mockImplementation(() => mockAuthService);
      LoginInput.mockImplementation(() => mockLoginInput);
      AuthOutput.mockImplementation(() => mockAuthOutput);
    });

    describe('successful login', () => {
      it('should return 200 and auth token for valid credentials', async () => {
        // Arrange
        const loginData = {
          email: '<EMAIL>',
          password: 'password123',
        };
        const sanitizedInput = {
          email: '<EMAIL>',
          password: 'password123',
        };
        const serviceResult = {
          user: {
            id: 1,
            name: 'Admin User',
            email: '<EMAIL>',
            role: 'admin',
          },
          authToken: 'jwt-token-here',
        };

        mockLoginInput.output.mockReturnValue(sanitizedInput);
        mockAuthService.login.mockResolvedValue(serviceResult);

        // Act
        const response = await request(app)
          .post('/api/v1/auth/login')
          .send(loginData);

        // Assert
        expect(response.status).toBe(200);
        expect(LoginInput).toHaveBeenCalledWith(loginData);
        expect(mockLoginInput.validate).toHaveBeenCalled();
        expect(mockLoginInput.output).toHaveBeenCalled();
        expect(mockAuthService.login).toHaveBeenCalledWith(sanitizedInput);
        expect(AuthOutput).toHaveBeenCalledWith(serviceResult.authToken, {
          user: serviceResult.user,
        });
        expect(mockAuthOutput.renderJson).toHaveBeenCalledWith(expect.any(Object));
      });

      it('should handle email normalization', async () => {
        // Arrange
        const loginData = {
          email: '  <EMAIL>  ',
          password: 'password123',
        };
        const sanitizedInput = {
          email: '<EMAIL>',
          password: 'password123',
        };
        const serviceResult = {
          user: { id: 1, email: '<EMAIL>' },
          authToken: 'jwt-token',
        };

        mockLoginInput.output.mockReturnValue(sanitizedInput);
        mockAuthService.login.mockResolvedValue(serviceResult);

        // Act
        await request(app).post('/api/v1/auth/login').send(loginData);

        // Assert
        expect(mockAuthService.login).toHaveBeenCalledWith(sanitizedInput);
      });
    });

    describe('validation errors', () => {
      it('should return 400 for missing email', async () => {
        // Arrange
        const loginData = { password: 'password123' };
        const validationError = new InvalidError('Validation failed', {
          details: [{ instancePath: '/email', message: 'must have required property email' }],
        });

        mockLoginInput.validate.mockImplementation(() => {
          throw validationError;
        });

        // Act
        const response = await request(app)
          .post('/api/v1/auth/login')
          .send(loginData);

        // Assert
        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
        expect(response.body).toHaveProperty('details');
        expect(mockAuthService.login).not.toHaveBeenCalled();
      });

      it('should return 400 for missing password', async () => {
        // Arrange
        const loginData = { email: '<EMAIL>' };
        const validationError = new InvalidError('Validation failed', {
          details: [{ instancePath: '/password', message: 'must have required property password' }],
        });

        mockLoginInput.validate.mockImplementation(() => {
          throw validationError;
        });

        // Act
        const response = await request(app)
          .post('/api/v1/auth/login')
          .send(loginData);

        // Assert
        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
        expect(mockAuthService.login).not.toHaveBeenCalled();
      });

      it('should return 400 for invalid email format', async () => {
        // Arrange
        const loginData = {
          email: 'invalid-email',
          password: 'password123',
        };
        const validationError = new InvalidError('Validation failed', {
          details: [{ instancePath: '/email', message: 'must match format "email"' }],
        });

        mockLoginInput.validate.mockImplementation(() => {
          throw validationError;
        });

        // Act
        const response = await request(app)
          .post('/api/v1/auth/login')
          .send(loginData);

        // Assert
        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
        expect(mockAuthService.login).not.toHaveBeenCalled();
      });
    });

    describe('authentication errors', () => {
      it('should return 404 for non-existent user', async () => {
        // Arrange
        const loginData = {
          email: '<EMAIL>',
          password: 'password123',
        };
        const sanitizedInput = {
          email: '<EMAIL>',
          password: 'password123',
        };

        mockLoginInput.output.mockReturnValue(sanitizedInput);
        mockAuthService.login.mockRejectedValue(new NotFoundError('User not found'));

        // Act
        const response = await request(app)
          .post('/api/v1/auth/login')
          .send(loginData);

        // Assert
        expect(response.status).toBe(404);
        expect(response.body).toHaveProperty('error', 'User not found');
        expect(mockAuthService.login).toHaveBeenCalledWith(sanitizedInput);
      });

      it('should return 400 for invalid password', async () => {
        // Arrange
        const loginData = {
          email: '<EMAIL>',
          password: 'wrongpassword',
        };
        const sanitizedInput = {
          email: '<EMAIL>',
          password: 'wrongpassword',
        };

        mockLoginInput.output.mockReturnValue(sanitizedInput);
        mockAuthService.login.mockRejectedValue(new InvalidError('Invalid password'));

        // Act
        const response = await request(app)
          .post('/api/v1/auth/login')
          .send(loginData);

        // Assert
        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Invalid password');
        expect(mockAuthService.login).toHaveBeenCalledWith(sanitizedInput);
      });
    });

    describe('server errors', () => {
      it('should return 500 for unexpected errors', async () => {
        // Arrange
        const loginData = {
          email: '<EMAIL>',
          password: 'password123',
        };
        const sanitizedInput = {
          email: '<EMAIL>',
          password: 'password123',
        };

        mockLoginInput.output.mockReturnValue(sanitizedInput);
        mockAuthService.login.mockRejectedValue(new Error('Database connection failed'));

        // Act
        const response = await request(app)
          .post('/api/v1/auth/login')
          .send(loginData);

        // Assert
        expect(response.status).toBe(500);
        expect(response.body).toHaveProperty('error', 'Internal server error');
        expect(mockAuthService.login).toHaveBeenCalledWith(sanitizedInput);
      });
    });

    describe('edge cases', () => {
      it('should handle empty request body', async () => {
        // Arrange
        const validationError = new InvalidError('Validation failed', {
          details: [
            { instancePath: '/email', message: 'must have required property email' },
            { instancePath: '/password', message: 'must have required property password' },
          ],
        });

        mockLoginInput.validate.mockImplementation(() => {
          throw validationError;
        });

        // Act
        const response = await request(app).post('/api/v1/auth/login').send({});

        // Assert
        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
        expect(response.body.details).toHaveLength(2);
      });

      it('should handle malformed JSON', async () => {
        // Act
        const response = await request(app)
          .post('/api/v1/auth/login')
          .set('Content-Type', 'application/json')
          .send('{"email": "<EMAIL>", "password":}'); // Invalid JSON

        // Assert
        expect(response.status).toBe(400);
      });
    });
  });
});
