const InvalidError = require('../errors/InvalidError');
const NotFoundError = require('../errors/NotFoundError');

class ApiController {
  createMethod(func) {
    return async (req, res, next) => {
      try {
        await func(req, res, next);
      } catch (error) {
        this.handleError(error, res, next);
      }
    };
  }

  /**
   * Handle errors in a consistent way across all controllers.
   * @param {Error} error - The error object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  handleError(error, res, next) {
    let backtrace = [];
    if (process.env.NODE_ENV !== 'production') {
      backtrace = error.stack.split('\n').map(line => line.trim());
    }

    // If response has already been sent, pass to error handler middleware
    if (res.headersSent) {
      return next(error);
    }

    if (error instanceof InvalidError) {
      return res.status(400).json({
        error: error.message || 'Validation failed',
        details: error.details,
        backtrace,
      });
    }

    if (error instanceof NotFoundError) {
      return res.status(404).json({
        error: error.message || 'Not found',
        details: [],
        backtrace,
      });
    }

    console.error('Controller error:', error);

    res.status(500).json({
      error: 'Internal server error',
      detail: undefined,
      backtrace,
    });
  }
}

module.exports = ApiController;
